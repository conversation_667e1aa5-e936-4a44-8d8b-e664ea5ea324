{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build:prd", "preview": "vite preview", "build:prd": "vite build --mode production", "build:preview": "vite build --mode preview", "build:test": "vite build --mode test", "build:staging": "vite build --mode staging", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint:fix": "eslint . --fix", "format": "prettier --write src/", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest --run"}, "dependencies": {"@fontsource-variable/jetbrains-mono": "^5.2.6", "@fontsource/alegreya": "^5.2.6", "@herm-studio/tool.js": "^0.0.8", "@microsoft/fetch-event-source": "^2.0.1", "@sentry/vue": "^8.54.0", "@vueuse/core": "^10.11.1", "amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "^1.11.0", "base-64": "^1.0.0", "browser-image-compression": "^2.0.2", "compressorjs": "^1.2.1", "copy-to-clipboard": "^3.3.3", "element-plus": "^2.10.7", "file-saver": "^2.0.5", "highlight.js": "^11.11.1", "hls.js": "^1.6.9", "hotkeys-js": "^3.13.15", "html-format": "^1.1.7", "html-to-image": "^1.11.13", "html2canvas": "^1.0.0", "html2pdf.js": "^0.10.3", "js-sha256": "^0.11.1", "jsbarcode": "^3.12.1", "katex": "^0.16.22", "large-small-dynamic-viewport-units-polyfill": "^0.1.1", "lodash": "^4.17.21", "lottie-web": "^5.13.0", "marked": "^7.0.5", "marked-highlight": "^2.2.2", "marked-katex-extension": "^5.1.5", "markmap-common": "^0.18.9", "markmap-lib": "^0.18.12", "markmap-render": "^0.18.12", "markmap-view": "^0.18.12", "md-editor-v3": "^4.2.2", "mixpanel-browser": "^2.67.0", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "monaco-editor-vue3": "^0.1.10", "mpg123-decoder": "^1.0.2", "nanoid": "^5.1.5", "pinia": "^3.0.3", "qr-code-styling": "^1.9.2", "qrcode": "^1.5.4", "recorder-core": "^1.2.23070100", "remixicon": "^4.6.0", "sanitize-html": "^2.17.0", "sass": "^1.77.8", "snabbdom": "^3.6.2", "snabby": "^6.1.1", "swiper": "^11.2.10", "textarea-caret": "^3.1.0", "tippy.js": "^6.3.7", "turndown": "^7.2.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "uuid": "^9.0.1", "viewerjs": "^1.11.7", "vue": "^3.5.18", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-google-login": "^2.0.34", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@chromatic-com/storybook": "^3.2.6", "@iconify/json": "^2.2.371", "@sentry/vite-plugin": "^2.22.7", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/preview-api": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/test-runner": "^0.23.0", "@storybook/vue3": "^8.6.14", "@storybook/vue3-vite": "^8.6.14", "@stylistic/stylelint-plugin": "^4.0.0", "@sxzz/popperjs-es": "^2.11.7", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@tsconfig/node18": "^2.0.1", "@types/base-64": "^1.0.2", "@types/chrome": "^0.0.326", "@types/lodash": "^4.17.20", "@types/mixpanel-browser": "^2.66.0", "@types/node": "^18.19.33", "@types/qrcode": "^1.5.5", "@types/sanitize-html": "^2.16.0", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.1", "@unocss/eslint-plugin": "^66.2.3", "@unocss/reset": "^66.2.3", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.33.0", "eslint-plugin-storybook": "^0.12.0", "jsdom": "^26.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "storybook": "^8.6.14", "stylelint": "^16.23.1", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard": "^39.0.0", "stylelint-config-standard-scss": "^15.0.1", "terser": "^5.43.1", "typescript": "~5.8.3", "unocss": "^66.2.3", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}