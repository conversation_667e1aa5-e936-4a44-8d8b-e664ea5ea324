<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="false"
    background-color="#F9FAFC"
    :full-screenable="false"
    :delay-content-on-fullscreen="true"
    :width="getPanelWidth"
  >
    <template #content>
      <section class="flex flex-1 flex-col px-50px py-24px">
        <div class="mb-16px text-24px text-#18181a font-450 font-Lexend">{{ pdbId }} {{ t('proteinPanel.title') }}</div>

        <!-- 3D 视图 Tab -->
        <div v-show="tab === 'info' && pdbId" class="flex-1 space-y-16px">
          <!-- Mol* 3D 渲染容器 -->
          <div class="size-full overflow-hidden border-2px border-#CEC9BA rounded-8px bg-white">
            <div class="relative size-full">
              <!-- Mol* 渲染容器 -->
              <div
                ref="molstarContainer"
                class="h-full w-full overflow-hidden rounded-8px bg-#F9FAFB"
              ></div>

              <!-- Loading 状态 -->
              <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center rounded-8px bg-#F9FAFB">
                <div class="flex flex-col items-center gap-12px">
                  <div class="h-32px w-32px animate-spin border-3px border-#E5E7EB border-t-#059669 rounded-full"></div>
                  <div class="text-14px text-#6B7280">Loading...</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细信息 Tab -->
        <div v-show="tab === 'details' && pdbId" class="flex-1 overflow-y-auto">
          <!-- Loading 状态 -->
          <div v-if="detailsLoading" class="flex items-center justify-center py-40px">
            <div class="flex flex-col items-center gap-12px">
              <div class="h-32px w-32px animate-spin border-3px border-#E5E7EB border-t-#059669 rounded-full"></div>
              <div class="text-14px text-#6B7280">{{ t('proteinPanel.loadingDetails') }}</div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="detailsError" class="py-40px text-center">
            <div class="mb-16px text-48px">⚠️</div>
            <div class="mb-8px text-16px text-#EF4444">{{ t('proteinPanel.errors.loadFailed') }}</div>
            <div class="text-14px text-#6B7280">{{ detailsError }}</div>
          </div>

          <!-- 详细信息内容 -->
          <div v-else-if="proteinDetails" class="space-y-24px">
            <!-- 主标题卡片 -->
            <div class="border border-#E5E7EB rounded-8px bg-white p-20px">
              <h3 class="mb-16px text-18px text-#18181a font-500">{{ t('proteinPanel.basicInfo.title') }}</h3>
              <el-descriptions :column="1" border>
                <el-descriptions-item :label="t('proteinPanel.basicInfo.structureTitle')">
                  <span class="text-16px text-#18181a font-500">{{ proteinDetails.struct?.title || t('proteinPanel.basicInfo.unknown') }}</span>
                </el-descriptions-item>
                <el-descriptions-item :label="t('proteinPanel.basicInfo.pdbId')">
                  <a
                    :href="`https://www.rcsb.org/structure/${proteinDetails.rcsb_id || proteinDetails.entry?.id}`"
                    target="_blank"
                    class="text-#059669 font-500 underline hover:text-#047857"
                  >
                    {{ proteinDetails.rcsb_id || proteinDetails.entry?.id }}
                  </a>
                </el-descriptions-item>
                <el-descriptions-item v-if="proteinDetails.struct_keywords?.text" :label="t('proteinPanel.basicInfo.keywords')">
                  <div class="flex flex-wrap gap-8px">
                    <el-tag
                      v-for="keyword in getKeywordTags(proteinDetails.struct_keywords.text)"
                      :key="keyword"
                      size="small"
                      type="info"
                    >
                      {{ keyword }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 发表文献卡片 -->
            <div v-if="proteinDetails.rcsb_primary_citation" class="border border-#E5E7EB rounded-8px bg-white p-20px">
              <h3 class="mb-16px text-18px text-#18181a font-500">{{ t('proteinPanel.publication.title') }}</h3>
              <el-descriptions :column="1" border>
                <el-descriptions-item :label="t('proteinPanel.publication.articleTitle')">
                  <span class="text-#18181a leading-relaxed">
                    {{ proteinDetails.rcsb_primary_citation.title }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item v-if="proteinDetails.rcsb_primary_citation.rcsb_authors" :label="t('proteinPanel.publication.authors')">
                  <span class="text-#18181a">
                    {{ formatAuthors(proteinDetails.rcsb_primary_citation.rcsb_authors) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item :label="t('proteinPanel.publication.journal')">
                  <span class="text-#18181a">
                    {{ proteinDetails.rcsb_primary_citation.rcsb_journal_abbrev }}, {{ proteinDetails.rcsb_primary_citation.year }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item v-if="proteinDetails.rcsb_primary_citation.pdbx_database_id_doi" :label="t('proteinPanel.publication.doi')">
                  <a
                    :href="`https://doi.org/${proteinDetails.rcsb_primary_citation.pdbx_database_id_doi}`"
                    target="_blank"
                    class="text-#059669 underline hover:text-#047857"
                  >
                    {{ proteinDetails.rcsb_primary_citation.pdbx_database_id_doi }}
                  </a>
                </el-descriptions-item>
                <el-descriptions-item v-if="proteinDetails.rcsb_primary_citation.pdbx_database_id_pub_med" label="PubMed">
                  <a
                    :href="`https://pubmed.ncbi.nlm.nih.gov/${proteinDetails.rcsb_primary_citation.pdbx_database_id_pub_med}`"
                    target="_blank"
                    class="text-#059669 underline hover:text-#047857"
                  >
                    {{ proteinDetails.rcsb_primary_citation.pdbx_database_id_pub_med }}
                  </a>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 实验信息卡片 -->
            <div class="border border-#E5E7EB rounded-8px bg-white p-20px">
              <h3 class="mb-16px text-18px text-#18181a font-500">{{ t('proteinPanel.experimental.title') }}</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item :label="t('proteinPanel.experimental.method')">
                  <span class="text-#18181a">
                    {{ proteinDetails.exptl?.[0]?.method || t('proteinPanel.basicInfo.unknown') }}</span>
                </el-descriptions-item>
                <el-descriptions-item :label="t('proteinPanel.experimental.resolution')">
                  <span class="text-#18181a">
                    {{
                      proteinDetails.rcsb_entry_info?.resolution_combined?.[0]
                        ? `${proteinDetails.rcsb_entry_info.resolution_combined[0]} Å`
                        : t('proteinPanel.basicInfo.unknown')
                    }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item :label="t('proteinPanel.experimental.composition')">
                  <span class="text-#18181a">
                    {{ proteinDetails.rcsb_entry_info?.polymer_composition || t('proteinPanel.basicInfo.unknown') }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item :label="t('proteinPanel.experimental.releaseDate')">
                  <span class="text-#18181a">
                    {{ formatDate(proteinDetails.rcsb_accession_info?.initial_release_date) }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <!-- 验证报告 Tab -->
        <div v-show="tab === 'validation' && pdbId" class="flex flex-1 flex-col">
          <!-- 检查中状态 -->
          <div v-if="validationPdfChecking" class="flex items-center justify-center py-40px">
            <div class="flex flex-col items-center gap-12px">
              <div class="h-32px w-32px animate-spin border-3px border-#E5E7EB border-t-#059669 rounded-full"></div>
              <div class="text-14px text-#6B7280">{{ t('proteinPanel.loadingValidation') }}</div>
            </div>
          </div>

          <!-- PDF可用时显示内容 -->
          <template v-else-if="validationPdfAvailable">
            <div class="mb-16px flex items-center justify-between">
              <h3 class="text-18px text-#18181a font-500">{{ t('proteinPanel.validation.title') }}</h3>
              <a
                :href="getPdfURL(pdbId)"
                target="_blank"
                class="flex items-center gap-8px text-14px text-#059669 underline hover:text-#047857"
              >
                <span>{{ t('proteinPanel.validation.openInNewWindow') }}</span>
                <svg class="h-16px w-16px" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>

            <div class="flex-1 overflow-hidden border border-#E5E7EB rounded-8px bg-white">
              <iframe
                :src="getPdfURL(pdbId)"
                class="size-full border-none"
                :title="t('proteinPanel.validation.pdfTitle')"
              />
            </div>
          </template>

          <!-- PDF不可用时的提示 -->
          <div v-else class="py-40px text-center">
            <div class="mb-16px text-48px">📄</div>
            <div class="mb-8px text-16px text-#6B7280">{{ t('proteinPanel.validation.unavailable') }}</div>
            <div class="text-14px text-#9CA3AF">{{ t('proteinPanel.validation.unavailableDesc') }}</div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-show="!pdbId || !tab" class="py-40px text-center">
          <div class="mb-16px text-48px">🧬</div>
          <div class="text-16px text-#6B7280">{{ t('proteinPanel.noData.title') }}</div>
        </div>
      </section>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import ResizablePanel from '@/components/ResizablePanel.vue'
import { useAppStore } from '@/stores/app'

interface Props {
  visible?: boolean
  pdbId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

// RCSB API 响应数据类型
interface ProteinDetails {
  entry?: {
    id: string
  }
  rcsb_id?: string
  struct?: {
    title: string
  }
  struct_keywords?: {
    text: string
  }
  rcsb_accession_info?: {
    initial_release_date: string
  }
  exptl?: Array<{
    method: string
  }>
  rcsb_entry_info?: {
    resolution_combined?: number[]
    polymer_composition?: string
  }
  rcsb_primary_citation?: {
    title: string
    rcsb_journal_abbrev: string
    year: number
    pdbx_database_id_doi?: string
    pdbx_database_id_pub_med?: number
    rcsb_authors?: string[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  pdbId: '',
})

const emits = defineEmits<Emits>()

const { t } = useI18n()

const visible = computed({
  get: () => props.visible,
  set: value => emits('update:visible', value),
})

const fullVisible = ref(false)
const tab = ref('info')
const showPdbTab = ref(true) // 是否显示3D结构那一页的Tab

const isLoading = ref(false)
const molstarContainer = ref<HTMLDivElement>()
let viewer: any = null

// 详细信息相关状态
const detailsLoading = ref(false)
const detailsError = ref<string>('')
const proteinDetails = ref<ProteinDetails | null>(null)

// 验证报告相关状态
const validationPdfAvailable = ref(false)
const validationPdfChecking = ref(false)

const appStore = useAppStore()
const { chatViewSectionWidth } = storeToRefs(appStore)
const getPanelWidth = computed(() => {
  // 因为滚动条宽度为8px，因为布局使用了scrollbar-gutter: stable，所以PC永远需要加8px
  return `${chatViewSectionWidth.value + 8}px`
})
const panelTabs = computed(() => {
  // 定义所有标签页的完整数组，确保顺序为 info -> details -> validation
  // condition为显示条件
  const allTabs = [
    {
      key: 'info',
      label: t('proteinPanel.tabs.info'),
      condition: () => showPdbTab.value,
    },
    {
      key: 'details',
      label: t('proteinPanel.tabs.details'),
      condition: () => true, // details 标签页始终显示
    },
    {
      key: 'validation',
      label: t('proteinPanel.tabs.validation'),
      condition: () => validationPdfAvailable.value,
    },
  ]

  // 根据条件过滤标签页
  return allTabs
    .filter(tab => tab.condition())
    .map(tab => ({
      key: tab.key,
      label: tab.label,
    }))
})

// 工具函数
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) { return t('proteinPanel.basicInfo.unknown') }
  try {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }
  catch {
    return dateString
  }
}

// 处理关键词标签
const getKeywordTags = (keywordsText: string): string[] => {
  if (!keywordsText) { return [] }
  // 按逗号分割关键词，并清理空白字符
  return keywordsText.split(',').map(keyword => keyword.trim()).filter(keyword => keyword.length > 0)
}

// 格式化作者列表
const formatAuthors = (authors: string[]): string => {
  if (!authors || authors.length === 0) { return t('proteinPanel.basicInfo.unknown') }
  return authors.join(', ')
}

// 构造验证报告PDF的URL
const getValidationPdfUrl = (pdbId: string): string => {
  if (!pdbId) { return '' }

  // 转换为小写
  const lowerPdbId = pdbId.toLowerCase()

  // 获取中间两位字符（第2和第3个字符）
  const middleTwoChars = lowerPdbId.substring(1, 3)

  // 构造URL：https://files.wwpdb.org/pub/pdb/validation_reports/{中间两位}/{完整PDB_ID}/{完整PDB_ID}_validation.pdf
  return `https://files.wwpdb.org/pub/pdb/validation_reports/${middleTwoChars}/${lowerPdbId}/${lowerPdbId}_validation.pdf`
}

const getPdfURL = (pdbId: string) => {
  const url = new URL('https://pdf-viewer.herm.studio/')
  const validationPdfUrl = getValidationPdfUrl(pdbId)
  url.searchParams.set('url', validationPdfUrl)
  return url.toString()
}

// 检查验证报告PDF是否可用
const checkValidationPdfAvailability = async (pdbId: string) => {
  if (!pdbId) {
    validationPdfAvailable.value = false
    return
  }

  try {
    validationPdfChecking.value = true
    const pdfUrl = getValidationPdfUrl(pdbId)

    // 使用HEAD请求检查PDF是否存在，避免下载整个文件
    const response = await fetch(pdfUrl, {
      method: 'HEAD',
      // 添加超时控制
      signal: AbortSignal.timeout(10000), // 10秒超时
    })

    // 检查响应状态和Content-Type
    const contentType = response.headers.get('content-type')
    const isValidPdf = response.ok
      && (contentType?.includes('application/pdf') ?? false)

    validationPdfAvailable.value = isValidPdf
  }
  catch (error) {
    console.warn('检查验证报告PDF可用性失败:', error)
    validationPdfAvailable.value = false
  }
  finally {
    validationPdfChecking.value = false
  }
}

// 获取蛋白质详细信息
const fetchProteinDetails = async (pdbId: string) => {
  if (!pdbId) { return }

  try {
    detailsLoading.value = true
    detailsError.value = ''

    const response = await fetch(`https://data.rcsb.org/rest/v1/core/entry/${pdbId.toUpperCase()}`)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    proteinDetails.value = data
  }
  catch (error) {
    console.error('获取蛋白质详细信息失败:', error)
    detailsError.value = error instanceof Error ? error.message : '获取数据失败'
    proteinDetails.value = null
  }
  finally {
    detailsLoading.value = false
  }
}

// 初始化 Mol* 视图
const initMolstar = async () => {
  if (!molstarContainer.value || !window.molstar) { return }

  try {
    // 创建 Mol* 视图
    viewer = await window.molstar.Viewer.create(molstarContainer.value, {
      layoutIsExpanded: false,
      layoutShowControls: true,
      layoutShowRemoteState: false,
      layoutShowSequence: true,
      layoutShowLog: false,
      layoutShowLeftPanel: true,
      viewportShowExpand: true,
      viewportShowSelectionMode: false,
      viewportShowAnimation: false,
    })
  }
  catch (error) {
    console.error('初始化 Mol* 失败:', error)
  }
}

// 加载 PDB 文件
const loadPdbFile = async (pdbId: string) => {
  if (!viewer || !pdbId) { return }

  try {
    isLoading.value = true

    // 清理旧的结构
    await viewer.plugin.clear()

    const url = `https://files.rcsb.org/download/${pdbId.toUpperCase()}.pdb`

    // 验证 PDB 文件 URL 是否存在
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(10000), // 10秒超时
      })

      if (!response.ok) {
        throw new Error(`PDB 文件不存在: HTTP ${response.status}`)
      }
    }
    catch (fetchError) {
      console.error('验证 PDB 文件失败:', fetchError)
      // PDB 文件不存在时，更新标签页状态并不执行加载
      showPdbTab.value = false
      tab.value = 'details'
      return
    }

    // URL 验证成功，继续加载结构
    await viewer.loadStructureFromUrl(url, 'pdb')
  }
  catch (error) {
    console.error('加载 PDB 文件失败:', error)
    // 加载失败时隐藏PDB标签页并切换到details标签页
    showPdbTab.value = false
    tab.value = 'details'
  }
  finally {
    isLoading.value = false
  }
}

// 核心初始化和加载函数
const initAndLoad = async () => {
  if (!molstarContainer.value || !window.molstar) { return }

  try {
    isLoading.value = true

    // 如果查看器不存在，先初始化
    if (!viewer) {
      await initMolstar()
    }

    // 如果有 pdbId，加载结构
    if (props.pdbId && viewer) {
      await loadPdbFile(props.pdbId)
    }
  }
  catch (error) {
    console.error('初始化和加载失败:', error)
  }
  finally {
    isLoading.value = false
  }
}

// 清理资源
const dispose = () => {
  if (viewer) {
    viewer.dispose()
    viewer = null
  }
}

// 清理所有数据残留
const clearAllData = () => {
  // 清理 Mol* 视图器
  dispose()

  // 清理详细信息数据
  proteinDetails.value = null
  detailsError.value = ''
  detailsLoading.value = false

  // 清理验证报告状态
  validationPdfAvailable.value = false
  validationPdfChecking.value = false

  // 重置加载状态
  isLoading.value = false

  // 重置PDB标签页状态
  showPdbTab.value = true

  // 重置 Tab 到默认状态
  tab.value = 'info'
}

// 监听 pdbId 变化
watch(() => props.pdbId, (newPdbId) => {
  if (newPdbId && viewer) {
    loadPdbFile(newPdbId)
  }
  // 清空详细信息数据，等待用户切换到详细信息 Tab 时重新加载
  proteinDetails.value = null
  detailsError.value = ''

  // 检查验证报告PDF是否可用
  checkValidationPdfAvailability(newPdbId)
})

// 监听 Tab 切换
watch(() => tab.value, (newTab) => {
  if (newTab === 'details' && props.pdbId && !proteinDetails.value && !detailsLoading.value) {
    fetchProteinDetails(props.pdbId)
  }
})

// 监听面板可见性变化
watch(() => props.visible, async (newVisible) => {
  if (newVisible && !viewer) {
    await nextTick()
    await initAndLoad()
  }
  else if (!newVisible) {
    // 面板关闭时清理所有数据残留
    clearAllData()
  }
})

// 组件挂载时初始化
onMounted(async () => {
  if (props.visible) {
    await nextTick()
    await initAndLoad()
  }

  // 如果有pdbId，检查验证报告PDF可用性
  if (props.pdbId) {
    checkValidationPdfAvailability(props.pdbId)
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  dispose()
})
</script>

<style scoped>
/* 如果需要特定样式可以在这里添加 */
</style>
