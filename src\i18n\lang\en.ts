export default {
  stripeView: {
    payNormalTip: 'Payment received! Click \'Continue\' to start using your Juchats premium account.',
    payErrorTip: 'Payment issue. Please contact customer service.',
    buttonText: 'Continue',
  },
  oauthView: {
    bindSucceed: 'Binding successful',
  },
  audioRecorder: {
    noSoundDetected: 'No sound detected, please try again',
    permissionsRejected: 'Microphone permissions rejected, please check your settings',
  },
  googleOauth: {
    tip: 'Google Redirecting...',
    loginRedirecting: 'Login expired, redirecting to login page',
  },
  chatView: {
    placeholder: 'Ask about \'{contextTitle}\'',
    fileTypeMessage: 'Current file type doesn\'t support attachments',
    modeMessage: 'Current model doesn\'t support attachments',
    cancelStarText: 'Unstar successful',
    saveStarText: 'Star saved successfully',
    upperLimitText: 'Star limit reached',
    moveGroupText: 'Added to group successfully',
    loading: 'Generating, please wait...',
    dialogPlaceholder: 'Enter conversation name',
    groupPlaceholder: 'Enter group name',
    warning: 'Error',
    fluxKontextProNoImageError: 'Please upload an image before performing image-to-image operations.',
    imageFailedToLoad: 'Image loading failed, please try again later.',
  },
  egg: {
    title: 'Surprise',
    message: 'Friends',
    skip: 'Skip',
    placeholder: 'Enter Surprise',
  },
  baseHeader: {
    title: 'Juchats Hermchats LLM Mixture model Natural language RAG Knowledge',
    twitter: 'Twitter',
    discord: 'Discord',
    home: 'Home',
    login: 'Log In',
  },
  baseFooter: {
    terms: 'Terms',
    privacyPolicy: 'Privacy Policy',
    client: 'Client',
    comingSoon: 'Coming Soon',
    connecting: 'CONNECTING',
  },
  voiceBar: {
    message: 'Error decoding audio file',
  },
  userInfo: {
    helpCenter: 'Help',
    client: 'Client',
    helpDocs: 'Help Docs',
    realtimeStatus: 'Realtime Status',
    mobile: 'Mobile',
    layOut: 'Log out',
    themeSetting: 'Theme',
    light: 'Light',
    dark: 'Dark',
    followSystem: 'System',
    codeExchange: 'Redeem',
    myInvite: 'Invitation',
    detail: 'Pricing',
    myProfile: 'Profile',
    updateLog: 'Update Log',
    languageSettings: 'Language',
    simplifiedChinese: 'Simplified Chinese',
    english: 'English',
    userCenter: 'User Center',
    appleSilicon: 'Apple Silicon',
    appleInter: 'Intel Chip',
    windows: 'Windows',
  },
  usageStatistics: {
    title: 'Statistics',
  },
  sideBar: {
    normalDialog: 'Chats',
    groupDialog: 'Group',
    createDialog: 'Start New Chat',
    clear: 'Clear',
    edit: 'Edit',
    cancel: 'Cancel',
    remove: 'Delete',
    share: 'Share',
    update: 'Edit',
    moveToGroup: 'Move to Group',
    resetTitle: 'Reset Title',
    resetTitleEmitSuccess: 'Resetting title...',
    dialogPlaceholder: 'Enter chat name',
    removeTitle: 'Are you sure you want to delete the group?',
    confirm: 'Confirm',
    groupPlaceholder: 'Enter group name',
    addGroup: 'Add Group',
    removeDialog: 'Delete Chat',
    willRemove: 'Will delete',
    irreversible: 'chat, this action is irreversible',
    deleteConfirmation: 'Confirm deletion ',
    allConversations: 'all ',
    deletePart: 'Delete these chat records?',
    someConversations: 'some ',
    conversations: 'chats ',
    below: ' below  ',
    shareDialog: 'Share Chat',
    shareChat: 'Share Chat',
    shareWebsite: 'Share Website',
    shareSelectorDialog: 'Share conversation',
    shareSelectorDialogCreate: 'Create Link',
    shareSelectorDialogDescription: 'Once your private information is shared, any messages added will remain confidential.',
    starDialog: 'Favorite',
    defaultGroup: 'Default',
    shareDialogSubtitle: 'will be shared and made public',
    contentAnalyze: 'Search Result',
    deleteAllListEmpty: 'The list is empty or all conversations are in the group, there are no conversations to delete',
  },
  shareFooter: {
    prompt: 'PROMPT',
    views: 'Views',
    cumulativeGain: 'Total Earnings',
    unlimitedLove: 'Unlimited Love',
    guest: 'Trial',
    deleted: 'This conversation has been deleted',
  },
  sendBox: {
    speechRecognition: 'Recognizing speech',
    maximumLimit: 'Maximum character limit exceeded',
    record: 'Press and hold the voice button to record',
    tip: '*Stability model only supports using English Prompts to generate images',
    exceed: 'Input content exceeds the limit',
  },
  sendAttachment: {
    unsupportedFormat: 'Unsupported file format',
    exceed: 'File size cannot exceed 15MB',
    imgExceed: 'Image size should be <5MB',
    uploadFail: 'Upload failed',
    loginFail: 'Login expired, redirecting to login page',
    cancelParsing: 'Parsing of attachment "{filename}" has been cancelled',
    notSupported: 'The current model does not support uploading attachments',
    onlyImage: 'The current model only supports uploading images',
    onlyFile: 'The current model only supports uploading files',
    pdfTooLong: 'PDF file cannot exceed %count pages',
  },
  sceneButton: {
    rolePlaying: 'Role-playing',
  },
  packageBox: {
    confirmPayment: 'Payment',
    waitPayment: 'Payment',
    cancelPayment: 'Cancel',
    renewalPurchase: 'Renew Subscription',
    jointPurchase: 'Joint Purchase',
    buyNow: 'Upgrade',
    currentMember: 'Current Package',
    upgraded: 'Upgraded',
    orderNumber: 'Order Number',
    packageCredit: 'Package Credit',
    rewardCredit: 'Reward Credit',
    actualPayments: 'Actual Payment',
    newUserDiscount: 'New User Discount',
    discountRegularUsers: 'Regular User Discount',
    packageSubscription: 'Pricing',
    eggCodeTip: 'Please enter surprise',
    exchange: 'Exchange',
    congratulate: 'Congratulations, you\'re now a {title} user!',
    subscriptionPackages: 'Subscription Packages',
    subscriptionTerm: 'Subscription Term',
    cutOffDate: 'Expiry Date',
    dailyPackage: 'Daily Package',
    monthlyPackage: 'Monthly Package',
    histories: 'History',
    starburst: 'Star',
    group: 'Group',
    freeMember: 'Free',
    basicMember: 'Trial',
    premiumMember: 'Plus',
    extremeMember: 'Premium',
    stripe: 'stripe',
    incentive: 'Reward',
    tipTitle: 'TIPS: ',
    planTip: '%title plan is a %type plan that resets every %days!',
    freeType: 'free',
    paidType: 'paid',
    expiryTime: 'Expiry',
  },
  notFound: {
    tip: 'We\'re sorry you\'ve encountered this error page. Due to limited development resources, the mobile page will be available later. Please look forward to it. Below is a',
    eggCode: 'Surprise',
    copyIt: 'Copy to use!',
    noInviteCode: 'No invitation codes available at the moment',
    error: 'error',
  },
  newPackageBox: {
    eachTime: 'times',
    month: 'month',
    day: 'day',
  },
  modelSelect: {
    modelName: 'Model includes',
    deepThinking: 'Deep Thinking',
    remainingCalls: 'Remaining number of calls',
  },
  loginWays: {
    login: 'login',
    email: 'email',
    emailPlaceholder: 'Enter Your Email Here',
    loginCodePlaceholder: 'Paste login code',
    submitButtonText: 'Continue with email',
    otherLoginText: 'Other Login Options',
    sendEmailMessage: 'Email verification code is valid for 5 minutes.',
    googleText: 'Continue with Google',
    githubText: 'Continue with Github',
    emailText: 'Sign Up with Email',
    emailFormatTip: 'Please enter a valid email address',
  },
  loginBox: {
    mobileLabel: 'Phone Number',
    mobilePlaceholder: 'Please enter phone number',
    codeLabel: 'SMS Verification Code',
    codePlaceholder: 'Please enter SMS verification code',
    sendAgain: 'Resend in {seconds}s',
    getCode: 'Get Code',
    inviteCode: 'Invited Code',
    agree: 'Agree to Terms',
    terms: 'Terms',
    policy: 'Policy',
    loginNow: 'Log In Now',
    mobileFormatTip: 'Please enter a valid phone number',
    codeFormatTip: 'Please enter a valid verification code',
    agreeTip: 'Please read and agree to the User Privacy / Usage / Disclaimer',
    newUserLoginTip: 'New users please register using Google, GitHub, or Email authorization!',
    ttpLoginTip: 'You\'ve already bound third-party login, please use Google, GitHub, or Email to log in!',
    codeTip: 'SMS verification code sent, please use within 5 minutes!',
  },
  imageGallery: {
    copy: 'Copy',
    copySuccess: 'Copied successfully!',
    copyText: 'Copy Text',
    closeButton: 'Close (Esc)',
    downloadButton: 'Download Image',
    copyImageButton: 'Copy Image',
  },
  headerMore: {
    export: 'Export',
    generatePNG: 'Exporting...',
    downloadPNG: 'The file has been downloaded',
    generatePDF: 'Exporting...',
    downloadPDF: 'The file has been downloaded',
    downloadSuccess: 'Export successful...',
    noTable: 'No table found in the current conversation',
    downloadError: 'Export failed',
  },
  headerBar: {
    communityBuilding: 'The number of users is currently surging, resources are tight, and an upgrade is in progress...',
    updateLog: 'Update Log',
    profileData: 'Profile',
    inviteAction: 'Invite',
    manageSubscription: 'Subscription',
    discoverEasterEgg: 'Easter Surprise',
    actionLogout: 'Logout',
    viewStatistics: 'Statistics',
    userProfile: 'My Profile',
    actionUnbind: 'Unbind',
    notBoundEmail: 'Email not bound yet',
    labelEmail: 'Email',
    actionBindEmail: 'Bind Email',
    sendCode: 'Send Code',
    enterYourEmail: 'Please enter your email',
    enterCode: 'Please enter verification code',
    resendCode: 'Resend in {sendSeconds}s',
    resendNow: 'Resend Now',
    labelPhone: 'Phone',
    confirmUnbinding: 'Confirm Unbinding',
    doNotSave: 'Don\'t Save',
    saveInfo: 'Save Info',
    membershipLevel: 'Membership Level',
    dailyConversationLimit: 'Daily Conversation Limit',
    modelUsageRestriction: 'Model Usage Restriction',
    unlimitedConversations: 'Unlimited Conversations',
    priorityOtherModels: 'Priority for Other Models',
    betaFeatureAccess: 'Beta Feature Access',
    subscriptionOffer: 'Subscription Offer Rights',
    subscriptionPlans: 'Subscription Plans',
    unlimitedAccess: 'Unlimited Access',
    daysValidity: 'Valid for {days} days',
    payImmediately: 'Pay Now',
    currentPlan: 'Current Plan',
    choosePaymentMethod: 'Payment Method',
    planPayment: 'Plan Payment',
    orderDetails: 'Order Details',
    paymentTimer: 'Payment Timer',
    packageOffset: 'Package Offset',
    dailyPackageDeduction: '(Original package deducted daily)',
    rewardOffset: 'Reward Offset',
    unitCurrency: 'Yuan',
    labelReward: 'Reward',
    weChatPayment: 'WeChat Pay',
    alipayPayment: 'Alipay',
    subscriptionStatus: 'Subscription Status',
    plusUserWelcome: 'Congratulations, you\'re now a Plus user!',
    orderStatus: 'Order Status',
    paymentStatus: 'Payment Status',
    paymentTotal: 'Payment Amount',
    numberInvited: 'Number of Invited',
    myReward: 'My Reward',
    myInviteCode: 'My Invited Code',
    copyInviteLink: 'Copy Link',
    nicknameUser: 'User Nickname',
    buySubscriptionPlan: 'Buy Subscription Plan',
    receiveReward: 'Receive Reward',
    timeOfPurchase: 'Purchase Time',
    brandReconstruction: 'Brand Reconstruction and <span class="red">Landing</span> Page',
    modelSupportGPT: 'Model Support for <span class="green">GPT Forte</span> Mixed Model',
    modelSupportClaude: 'Model Support for <span class="green">Claude Forte</span> Mixed Model',
    modelSupportMixtral: 'Model Support for <span class="green">Mixtral Forte</span> Mixed Model',
    modelSupportDALL: 'Model Support for Independent Use of DALL·E3',
    modelSupportLlama: 'Model Support for Llama3 70B Trial Use',
    optimizeLeTax: 'Optimize LeTax Formula Display Style',
    optimizeMarkdown: 'Optimize Markdown and Code Block Display Style',
    optimizeConnection: 'Optimize Internet Connection Speed and Accuracy',
    fixBugs: 'Fix Various Bugs and Experience Issues <span class="blue">via. Discord</span>',
    subscriptionOpen: 'Subscription Open for Some Package Subscriptions',
    subscriptionUnlimited: 'Plus and Higher Members Have Unlimited Internet Usage',
    subscriptionHistory: 'Plus and Higher Members Have Unlimited History Records',
    paymentProcessing: 'Processing Payment',
    paymentSuccessful: 'Payment Successful',
    paymentCancelled: 'Payment Cancelled',
    transactionClosed: 'Transaction Closed',
    refundedAmount: 'Refunded',
    contactSupport: 'Contact Support',
    phoneFormatError: 'Phone format error',
    successfulUnbind: 'Unbound successfully',
    enterCorrectEmailAccount: 'Please enter a correct email account',
    successfulBinding: 'Bound successfully',
    confirmUnbindPhone: '* Click \'Confirm Unbinding\' to unbind and delete the phone number',
    successfulModification: 'Modified successfully',
    infoSavedSuccessfully: 'Saved successfully',
    free: 'Free',
    plus: 'Plus',
    avatarLimit: 'Avatar size can not exceed 5MB',
    avatarFormat: 'Avatar must be JPG or PNG format',
    avatarSuccess: 'Avatar uploaded successfully',
    avatarError: 'Avatar uploaded failed',
  },
  footerBar: {
    privacyPolicy: 'Privacy policy',
    termsUse: 'Terms of use',
  },
  emailBox: {
    emailAddress: 'Email Address',
    promptEnterEmail: 'Please enter email address',
    emailVerificationCode: 'Email Verification Code',
    promptEnterVerificationCode: 'Please enter email verification code',
    promptEnterInvitationCode: 'Please enter invited code',
    agreeToTerms: 'Agree to terms',
    fillEmailAddress: 'Please fill in email address',
    fillVerificationCode: 'Please fill in verification code',
  },
  eggRedemption: {
    eggCodeExchange: 'Redeem Code',
    eggCodePlaceholder: 'Please enter code',
    exchange: 'Exchange',
  },
  dragAttachmentPlaceHolder: {
    placeholder: 'Drag files to chat box, max {ATTACHMENTS_NUMBER_LIMIT} files, each file needs',
  },
  dialog: {
    confirm: 'Confirm',
  },
  controlBar: {
    clear: 'Clear',
    confirmText: 'Delete all conversations?',
    clearDialogTitle: 'Clear Chat',
    clearDialogSuccess: 'Clear successfully',
    clearDialogContent: 'Are you sure you want to clear the current conversation context? This action cannot be undone.',
  },
  chatContent: {
    regenerate: 'Regenerate',
    networkSearch: 'Internet',
    relatedQuestions: 'Related',
    question: 'Question',
    image: 'Image',
    errorText: 'Sorry, there was an error in the current conversation',
    errorButton: 'Click to retry',
    viewFullMessage: 'View Full Message',
    mindMap: 'Mind',
    editorItem: 'Edit',
    editorItemSave: 'Save',
    editorItemSend: 'Send',
    noContentModification: 'No content modification',
    invalidContent: 'Content cannot be empty',
    lossContent: 'Lack of Internet data',
    dataError: 'Chat failed or timed out',
    toggleRenderMode: 'Toggle Render Mode',
  },
  bindLogin: {
    authLogin: 'OAuth Login',
    googleAuthorized: 'Google authorized',
    githubAuthorized: 'Github authorized',
    bind: 'Bind',
    googleTip: 'We recommend using Google authorization for secure and quick login. This is the first step towards product decentralization!',
  },
  billDetail: {
    start: 'Start Journey',
    serialNumber: 'SERIAL NUMBER',
  },
  attachmentCard: {
    parseFailure: 'Parse failed',
    parsing: 'Parsing',
    parseSuccess: 'Parse successful!',
  },
  personalCenterInfo: {
    nickName: 'Nickname',
    unbindedEmail: 'Email not bound yet',
    unbindedLogin: 'Not bound',
    save: 'Save',
  },
  personalCenterInvitation: {
    userAccount: 'User Account',
    rewards: 'Reward Amount',
  },
  personalCenterLayout: {
    mySubscription: 'Subscription',
    myCenter: 'Personal Center',
  },
  personalCenterSubscription: {
    status: 'Current Status',
    payAmount: 'Payment Amount',
    payTime: 'Payment Time',
    relatedOperations: 'Related Operations',
    pay: 'Pay',
    view: 'View',
  },
  eGGSuccess: {
    greet: 'Hi,',
    congratulations: 'Congratulations',
    subscribePackage: 'Subscribe Package',
    cycle: 'Cycle',
    deadline: 'Deadline',
    serialNumber: 'serial number',
  },
  tipMessage: {
    copyLinkSuccess: 'Copy Link',
    loginFail: 'Login failed. Redirecting...',
    upgradeTip: 'Package used up,',
    upgrade: 'please upgrade',
    error: 'System exception, please try again later',
    copySuccess: 'Copy success',
    copyTextSuccess: 'Copy Text success',
    copyMarkdownSuccess: 'Copy Markdown success',
    copyFail: 'Copy fails',
    imgPlaceholder: 'Type a description of the image to be generated',
    filePlaceholder: 'Message Juchats and upload or paste files',
    questionPlaceholder: 'Please enter your question',
    oneDay: 'One day',
    fiveDays: 'Five days',
    sevenDays: 'Seven days',
    fifteenDays: 'Fifteen days',
    oneMonth: 'A month',
    oneYear: 'One year',
    day: 'day',
    expiry: 'The subscription has expired',
    renewal: 'Immediate renewal',
    llmStreaming: 'LLM is generating, please wait...',
    saveSuccess: 'Save Successful',
  },
  tools: {
    MERMAID: 'Mermaid Charts Generation',
    IMAGE_CREATION: 'Create Images',
    DATA_ANALYSIS: 'Data Analysis',
    BROWSING: 'Browsing',
    SEARCH_URL: 'Search URLs',
    SEARCH_FILES: 'Search Files',
    SUMMARIZE_URL: 'Summarize URLs',
    SUMMARIZE_FILE: 'Summarize File',
    X_INFO: '𝕏 Searching',
    MAP_GENERATION: 'Map Generation',
    ARTIFACTS: 'Artifacts Generation',
    CHOOSE_TOOLS: 'Activating Tools',
    NETWORK_UNSTABLE: 'The network is still, content is coming soon',
    NETWORK_UNSTABLE_ERROR: 'Request exception, please try again',
    NETWORK_TIMEOUT_ERROR: 'Network request timeout, please try again later',
    NETWORK_MAX_RETRIES_ERROR: 'Too many retries, please try again later',
    TOOL_CALL_TIMEOUT: 'The tool is on strike. Refresh and retry',
    CHEMISTRY: 'Rendering chemical formula',
    STRUCTURAL_BIOLOGY: 'Searching for related information',
  },
  guildPrompt: {
    summaryTweets: 'Summary Tweets',
    appleFinancial: 'Apple Financial',
    mermaid: 'Mermaid',
    createImage: 'Create Image',
    travelPlan: 'Travel Plan',
    documentInterpretation: 'Document Interpretation',
    summaryLink: 'Summary Link',
    summaryTweetsPrompt: '{\'Showcasing @Cydiar404’s top 10 tweets (display image) with sarcastic comments.\'}',
    appleFinancialPrompt: '2024 latest Apple 10-K financial report analysed and trends charted',
    mermaidPrompt: 'Help me sort out the timeline and character relationships of 100 Years of Solitude through relationship mapping',
    createImagePrompt: 'Create a Van Gogh starry sky style sunset! Horizontal one',
    travelPlanPrompt: 'Recommended itinerary for a three-day trip to Shanghai, labelled by a map!',
    documentInterpretationPrompt: 'Read 《底层逻辑》 and summarise it!',
    summaryLinkPrompt: 'https://medium.com/vectrix-ai/how-to-implement-a-react-flow-using-langgraph-studio-5e4b859b5506\n\n in-depth summary of the above links',
    mixedMode: 'Mixed Mode',
    mixedModePrompt: 'https://timkellogg.me/blog/2025/02/03/s1\nIn-depth summary of this article, needs to be very detailed, include complete image links displayed in necessary places! ',
    twitterThread: 'Twtter Thread',
    twitterThreadPrompt: 'Please retrieve all threads of the tweet at "https://x.com/omarsar0/status/1891705029083512934", without summarizing, return the original text in full, preserving Markdown format.',
  },
  markMap: {
    title: 'Mind',
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    fitScreen: 'Fit Screen',
    download: 'Download',
    fullscreen: 'Fullscreen',
    exitEditMode: 'Exit Edit Mode',
    enterEditMode: 'Edit Mode',
  },
  artifactsCodeHtml: {
    screenshotGenerating: 'Generating...',
    screenshotSuccess: 'Generated successfully',
    close: 'Close',
    fullScreen: 'Fullscreen',
    screenshot: 'Screenshot',
    download: 'Download',
    elementPicker: 'Element Picker',
    lockedElement: 'Element is locked',
    elementDescriptionPlaceholder: 'Enter instructions or description for this element...',
    enterEditMode: 'Enter image text editing mode',
    exitEditMode: 'Exit Edit Image Text Mode',
    saveAndGetHtml: 'Save',
    imageEditTitle: 'Edit Image',
    imageEditPlaceholder: 'Please enter image URL...',
    imageEditConfirm: 'Confirm',
    imageEditCancel: 'Cancel',
    imageEditUploadText: 'Click to select image file',
    imageEditUploading: 'Uploading...',
    imageEditUploadSuccess: 'Upload successful',
    imageEditUploadFailed: 'Upload failed',
    imageEditTabUrl: 'URL Address',
    imageEditTabUpload: 'Upload File',
    imageEditTabIcon: 'Select Icon',
  },
  iconSelector: {
    loadingIcons: 'Loading icons...',
    retryLoadIcons: 'Retry',
    iconSearchPlaceholder: 'Search icons...',
    noIconsFound: 'No icons found',
    tryDifferentKeywords: 'Try different keywords',
    loadMoreIcons: 'Load More Icons',
    loadingMoreIcons: 'Loading more icons...',
    allIconsLoaded: 'All icons loaded',
    totalIcons: 'Total {total} icons, {loaded} loaded',
  },
  updateNote: {
    title: 'Release Note',
  },
  claude4thinking: 'Thinking...',
  chatHistoryList: {
    all: 'All',
    today: 'Today',
    week: 'Week',
    month: 'Month',
    earlier: 'Earlier',
  },
  proteinPanel: {
    title: 'Protein Information',
    tabs: {
      info: 'Protein Info',
      details: 'Details',
      validation: 'Report',
    },
    loadingDetails: 'Loading details...',
    loadingValidation: 'Checking validation report...',
    errors: {
      loadFailed: 'No relevant information yet',
      validationUnavailable: 'Validation report unavailable',
      validationUnavailableDesc: 'No validation report available for this PDB structure',
    },
    noData: {
      title: 'No protein information available',
    },
    basicInfo: {
      title: '[Basic Information]',
      structureTitle: 'Structure Title',
      pdbId: 'PDB ID',
      keywords: 'Keywords',
      unknown: 'Unknown',
    },
    publication: {
      title: '[Publication]',
      articleTitle: 'Article Title',
      authors: 'Authors',
      journal: 'Journal',
      doi: 'DOI',
    },
    experimental: {
      title: '[Experimental Information]',
      method: 'Experimental Method',
      resolution: 'Resolution',
      composition: 'Molecular Composition',
      releaseDate: 'Release Date',
    },
    validation: {
      title: '[Validation Report]',
      openInNewWindow: 'Open in new window',
      pdfTitle: 'PDB Validation Report',
      unavailable: 'Validation report unavailable',
      unavailableDesc: 'No validation report available for this PDB structure',
    },
  },
}
