<template>
  <div class="">
    <!-- 加载状态 -->
    <div v-if="iconsLoading" class="flex-c py-40px">
      <div class="flex items-center gap-8px text-gray-500">
        <i class="i-line-md-loading-twotone-loop text-16px"></i>
        <span class="text-sm">{{ $t('iconSelector.loadingIcons') }}</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="iconsLoadError" class="flex-c py-40px">
      <div class="text-center">
        <div class="mb-8px text-sm text-red-500">{{ iconsLoadError }}</div>
        <button
          class="text-sm text-blue-500 hover:text-blue-600"
          @click="fetchIcons"
        >
          {{ $t('iconSelector.retryLoadIcons') }}
        </button>
      </div>
    </div>

    <!-- 正常状态 -->
    <div v-else>
      <!-- 图标库选择器 -->
      <div class="mb-15px">
        <el-segmented
          v-model="selectedLibrary"
          :options="iconLibraries"
          :props="{ value: 'key', label: 'name' }"
        />
      </div>

      <!-- 搜索框 -->
      <div class="mb-15px">
        <el-input
          v-model="iconSearchQuery"
          type="text"
          size="default"
          :placeholder="$t('iconSelector.iconSearchPlaceholder')"
          :clearable="true"
        />
      </div>

      <!-- 图标网格 -->
      <div v-if="filteredIcons.length > 0" class="icon-selector">
        <el-scrollbar
          height="300px"
          :distance="50"
          @end-reached="handleEndReached"
        >
          <div class="grid grid-cols-8 gap-8px p-2px">
            <div
              v-for="icon in filteredIcons"
              :key="icon"
              class="size-40px flex-c cursor-pointer border border-gray-200 rounded-md transition-colors hover:border-blue-400 hover:bg-blue-50"
              :class="{
                'border-blue-500 bg-blue-100': selectedIcon === icon,
              }"
              :title="icon"
              @click="selectIcon(icon)"
            >
              <img
                :src="getIconUrl(icon)"
                :alt="icon"
                class="size-20px"
                loading="lazy"
              >
            </div>
          </div>

          <!-- 加载状态指示器 -->
          <div v-if="isLoadingMore" class="flex-c py-15px">
            <div class="flex items-center gap-8px text-gray-500">
              <i class="i-line-md-loading-twotone-loop text-16px"></i>
              <span class="text-sm">{{ $t('iconSelector.loadingMoreIcons') }}</span>
            </div>
          </div>

          <!-- 已加载完成提示 -->
          <div v-else-if="allIconsLoaded && filteredIcons.length > 0" class="flex-c py-15px">
            <div class="text-center text-xs text-gray-400">
              {{ $t('iconSelector.allIconsLoaded') }}
            </div>
          </div>

          <!-- 图标总数显示 -->
          <div v-if="currentIcons.length > 0" class="pb-10px text-center text-xs text-gray-400">
            {{ $t('iconSelector.totalIcons', { count: currentIcons.length, loaded: displayedIconsCount, total: filteredIconsList.length }) }}
          </div>
        </el-scrollbar>
      </div>

      <!-- 无搜索结果 -->
      <div v-else-if="iconSearchQuery.trim()" class="flex-c py-40px">
        <div class="text-center text-gray-500">
          <div class="text-sm">{{ $t('iconSelector.noIconsFound') }}</div>
          <div class="mt-4px text-xs">{{ $t('iconSelector.tryDifferentKeywords') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<Emits>()

interface Emits {
  (e: 'select', iconName: string, libraryPrefix: string): void
}

// 图标库定义
interface IconLibrary {
  key: string
  name: string
  prefix: string
  apiUrl: string
}

const iconLibraries: IconLibrary[] = [
  {
    key: 'lucide',
    name: 'Lucide',
    prefix: 'lucide',
    apiUrl: 'https://api.iconify.design/collection?prefix=lucide',
  },
  {
    key: 'remix',
    name: 'Remix Icon',
    prefix: 'ri',
    apiUrl: 'https://api.iconify.design/collection?prefix=ri',
  },
]

const selectedIcon = ref('')
const iconSearchQuery = ref('')
const displayedIconsCount = ref(80) // 初始显示的图标数量
const allIconsLoaded = ref(false)
const selectedLibrary = ref('lucide') // 默认选择 Lucide
const isLoadingMore = ref(false) // 加载更多图标的状态

// 动态获取的图标列表
const lucideIcons = ref<string[]>([])
const remixIcons = ref<string[]>([])
const iconsLoading = ref(false)
const iconsLoadError = ref('')

// 获取当前选中库的图标列表
const currentIcons = computed(() => {
  return selectedLibrary.value === 'lucide' ? lucideIcons.value : remixIcons.value
})

// 获取图标 URL
function getIconUrl(iconName: string): string {
  const library = iconLibraries.find(lib => lib.key === selectedLibrary.value)
  if (!library) { return '' }

  return `https://api.iconify.design/${library.prefix}:${iconName}.svg`
}

// 监听图标库选择变化
watch(selectedLibrary, () => {
  resetState()
  fetchIcons()
})

// 获取 Lucide 图标列表
async function fetchLucideIcons() {
  if (lucideIcons.value.length > 0) { return } // 已经加载过了

  try {
    const response = await fetch('https://api.iconify.design/collection?prefix=lucide')
    if (response.ok) {
      const data = await response.json()
      // 获取所有可见的图标
      // API 返回格式：{ uncategorized: [...], hidden: [...], aliases: {...} }
      lucideIcons.value = data.uncategorized || []
    }
    else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }
  catch (error) {
    console.error('Failed to fetch Lucide icons:', error)
    // 提供一些基础图标作为后备
    lucideIcons.value = [
      'house',
      'user',
      'settings',
      'search',
      'heart',
      'star',
      'bookmark',
      'bell',
      'mail',
      'phone',
      'camera',
      'image',
      'video',
      'music',
      'file',
      'folder',
      'download',
      'upload',
      'share',
      'link',
      'copy',
      'square-pen',
      'trash',
      'plus',
      'minus',
      'check',
      'x',
      'arrow-left',
      'arrow-right',
      'arrow-up',
      'arrow-down',
    ]
    throw error
  }
}

// 获取 Remix Icon 图标列表
async function fetchRemixIcons() {
  if (remixIcons.value.length > 0) { return } // 已经加载过了

  try {
    const response = await fetch('https://api.iconify.design/collection?prefix=ri')
    if (response.ok) {
      const data = await response.json()
      // Remix Icon API 返回格式：{ categories: { "Arrows": [...], "Buildings": [...], ... } }
      const allIcons: string[] = []
      if (data.categories) {
        Object.values(data.categories).forEach((categoryIcons: any) => {
          if (Array.isArray(categoryIcons)) {
            allIcons.push(...categoryIcons)
          }
        })
      }
      remixIcons.value = allIcons
    }
    else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }
  catch (error) {
    console.error('Failed to fetch Remix icons:', error)
    // 提供一些基础图标作为后备
    remixIcons.value = [
      'home-line',
      'user-line',
      'settings-line',
      'search-line',
      'heart-line',
      'star-line',
      'bookmark-line',
      'notification-line',
      'mail-line',
      'phone-line',
      'camera-line',
      'image-line',
      'video-line',
      'music-line',
      'file-line',
      'folder-line',
      'download-line',
      'upload-line',
      'share-line',
      'link',
      'file-copy-line',
      'edit-line',
      'delete-bin-line',
      'add-line',
      'subtract-line',
      'check-line',
      'close-line',
      'arrow-left-line',
      'arrow-right-line',
      'arrow-up-line',
      'arrow-down-line',
    ]
    throw error
  }
}

// 统一的获取图标函数
async function fetchIcons() {
  iconsLoading.value = true
  iconsLoadError.value = ''

  try {
    if (selectedLibrary.value === 'lucide') {
      await fetchLucideIcons()
    }
    else if (selectedLibrary.value === 'remix') {
      await fetchRemixIcons()
    }
  }
  catch {
    iconsLoadError.value = '加载图标列表失败，请检查网络连接'
  }
  finally {
    iconsLoading.value = false
  }
}

// 过滤后的图标列表（不包含数量限制）
const filteredIconsList = computed(() => {
  let icons = currentIcons.value

  // 根据搜索查询过滤
  if (iconSearchQuery.value.trim()) {
    const query = iconSearchQuery.value.toLowerCase().trim()
    icons = icons.filter((icon: string) => icon.includes(query))
  }

  return icons
})

// 显示的图标列表（包含数量限制）
const filteredIcons = computed(() => {
  return filteredIconsList.value.slice(0, displayedIconsCount.value)
})

// 监听状态变化，更新 allIconsLoaded
watchEffect(() => {
  allIconsLoaded.value = displayedIconsCount.value >= filteredIconsList.value.length
})

// 选择图标
function selectIcon(iconName: string) {
  selectedIcon.value = iconName
  const library = iconLibraries.find(lib => lib.key === selectedLibrary.value)
  const libraryPrefix = library?.prefix || 'lucide'
  emit('select', iconName, libraryPrefix)
}

// 处理滚动到底部事件
function handleEndReached(direction: 'top' | 'bottom' | 'left' | 'right') {
  // 只处理向下滚动到底部的情况
  if (direction === 'bottom' && !allIconsLoaded.value && !isLoadingMore.value) {
    loadMoreIcons()
  }
}

// 加载更多图标
async function loadMoreIcons() {
  if (isLoadingMore.value || allIconsLoaded.value) {
    return
  }

  isLoadingMore.value = true

  // 模拟加载延迟，提供更好的用户体验
  await new Promise(resolve => setTimeout(resolve, 300))

  const increment = 80
  const newCount = displayedIconsCount.value + increment
  const totalIcons = filteredIconsList.value.length

  if (newCount >= totalIcons) {
    displayedIconsCount.value = totalIcons
  }
  else {
    displayedIconsCount.value = newCount
  }

  isLoadingMore.value = false
}

// 重置状态
function resetState() {
  selectedIcon.value = ''
  iconSearchQuery.value = ''
  displayedIconsCount.value = 80
  isLoadingMore.value = false
}

// 监听 visible 变化，当组件显示时自动加载图标
watch(() => props.visible, (newVisible) => {
  if (newVisible && currentIcons.value.length === 0 && !iconsLoading.value) {
    fetchIcons()
  }
  if (!newVisible) {
    resetState()
  }
}, { immediate: true }) // 立即执行一次，检查初始状态

// 暴露方法给父组件
defineExpose({
  fetchIcons,
  resetState,
  selectedIcon: readonly(selectedIcon),
})
</script>

<style lang="scss" scoped>
// 样式由 Element Plus Scrollbar 组件处理
</style>
